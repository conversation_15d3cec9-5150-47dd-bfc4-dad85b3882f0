import cv2
import numpy as np
from ultralytics import YOLO
import time

class SingleDetectionCamera:
    def __init__(self, model_path='sibiv3.pt', camera_index=0, min_confidence=0.3):
        """
        Single Detection Indonesian Sign Language Camera
        Shows only the highest confidence detection per frame
        
        Args:
            model_path (str): Path to YOLO model
            camera_index (int): Camera index
            min_confidence (float): Minimum confidence to show detection
        """
        self.model_path = model_path
        self.camera_index = camera_index
        self.min_confidence = min_confidence
        self.model = None
        self.cap = None
        
        # Indonesian Sign Language classes
        self.class_names = {
            0: 'di', 1: 'hotel', 2: 'jalan', 3: 'kamu', 4: 'ke', 
            5: 'makan', 6: 'mana', 7: 'mau', 8: 'saya'
        }
        
        self.load_model()
        self.setup_camera()
    
    def load_model(self):
        """Load YOLO model"""
        try:
            print(f"🔄 Loading model from {self.model_path}...")
            self.model = YOLO(self.model_path)
            print(f"✅ Model loaded successfully!")
            print(f"📋 Classes: {list(self.model.names.values())}")
            
        except Exception as e:
            print(f"❌ Error loading model: {e}")
            self.model = None
    
    def setup_camera(self):
        """Initialize camera"""
        try:
            self.cap = cv2.VideoCapture(self.camera_index)
            if not self.cap.isOpened():
                raise Exception("Cannot open camera")
            
            # Set camera properties
            self.cap.set(cv2.CAP_PROP_FRAME_WIDTH, 640)
            self.cap.set(cv2.CAP_PROP_FRAME_HEIGHT, 480)
            self.cap.set(cv2.CAP_PROP_FPS, 30)
            
            print("📹 Camera initialized successfully")
            
        except Exception as e:
            print(f"❌ Error initializing camera: {e}")
            self.cap = None
    
    def get_single_detection(self, frame):
        """
        Get single best detection from frame
        
        Args:
            frame: OpenCV frame
            
        Returns:
            tuple: (class_name, confidence, box) or (None, 0, None) if no detection
        """
        if self.model is None:
            return None, 0.0, None
        
        try:
            # Run inference
            results = self.model.predict(
                frame, 
                conf=0.15,      # Lower threshold to catch more possibilities
                iou=0.45,       
                max_det=20,     # Allow multiple detections to find best
                verbose=False   
            )
            
            if len(results) == 0 or len(results[0].boxes) == 0:
                return None, 0.0, None
            
            # Get all detections
            boxes = results[0].boxes.xyxy.cpu().numpy()
            confidences = results[0].boxes.conf.cpu().numpy()
            classes = results[0].boxes.cls.cpu().numpy().astype(int)
            
            # Find highest confidence detection
            best_idx = confidences.argmax()
            best_conf = confidences[best_idx]
            
            # Check if confidence meets minimum threshold
            if best_conf < self.min_confidence:
                return None, 0.0, None
            
            best_cls = classes[best_idx]
            best_box = boxes[best_idx]
            
            # Get class name
            class_name = self.model.names.get(best_cls, f"Class_{best_cls}")
            
            return class_name, best_conf, best_box
            
        except Exception as e:
            print(f"❌ Detection error: {e}")
            return None, 0.0, None
    
    def draw_single_detection(self, frame, class_name, confidence, box):
        """
        Draw single detection on frame
        
        Args:
            frame: OpenCV frame
            class_name: Detected class name
            confidence: Detection confidence
            box: Bounding box coordinates
        """
        height, width = frame.shape[:2]
        
        if class_name is None:
            # No detection
            cv2.putText(frame, "No sign detected", (10, 50), 
                       cv2.FONT_HERSHEY_SIMPLEX, 1.2, (0, 0, 255), 3)
            
            # Show minimum confidence requirement
            cv2.putText(frame, f"Min confidence: {self.min_confidence:.0%}", 
                       (10, height - 30), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (128, 128, 128), 2)
            return
        
        # Draw bounding box
        x1, y1, x2, y2 = box.astype(int)
        
        # Color based on confidence
        if confidence > 0.8:
            color = (0, 255, 0)      # Bright green - very high confidence
            status = "EXCELLENT"
        elif confidence > 0.6:
            color = (0, 255, 255)    # Yellow - good confidence  
            status = "GOOD"
        elif confidence > 0.4:
            color = (0, 165, 255)    # Orange - medium confidence
            status = "MEDIUM"
        else:
            color = (0, 100, 255)    # Red-orange - low confidence
            status = "LOW"
        
        # Draw thick bounding box
        cv2.rectangle(frame, (x1, y1), (x2, y2), color, 4)
        
        # Draw main detection text (large)
        main_text = f"{class_name.upper()}"
        cv2.putText(frame, main_text, (10, 60), 
                   cv2.FONT_HERSHEY_SIMPLEX, 2.0, color, 4)
        
        # Draw confidence and status
        conf_text = f"{confidence:.1%} ({status})"
        cv2.putText(frame, conf_text, (10, 110), 
                   cv2.FONT_HERSHEY_SIMPLEX, 1.0, (255, 255, 255), 2)
        
        # Draw label on bounding box
        label = f"{class_name}: {confidence:.2f}"
        label_size = cv2.getTextSize(label, cv2.FONT_HERSHEY_SIMPLEX, 0.8, 2)[0]
        
        # Label background
        cv2.rectangle(frame, (x1, y1 - label_size[1] - 15), 
                     (x1 + label_size[0] + 10, y1), color, -1)
        
        # Label text
        cv2.putText(frame, label, (x1 + 5, y1 - 8), 
                   cv2.FONT_HERSHEY_SIMPLEX, 0.8, (0, 0, 0), 2)
        
        # Draw center point
        center_x = (x1 + x2) // 2
        center_y = (y1 + y2) // 2
        cv2.circle(frame, (center_x, center_y), 5, color, -1)
    
    def run(self):
        """Run the single detection camera"""
        if self.cap is None or self.model is None:
            print("❌ Camera or model not available")
            return
        
        print("🎯 Starting Single Detection Mode...")
        print(f"📋 Detectable signs: {list(self.model.names.values())}")
        print(f"🎚️  Minimum confidence: {self.min_confidence:.0%}")
        print("\nControls:")
        print("  'q' - Quit")
        print("  's' - Save current frame")
        print("  '+' - Increase confidence threshold")
        print("  '-' - Decrease confidence threshold")
        print("  'r' - Reset confidence to default")
        
        frame_count = 0
        last_detection = "None"
        detection_start_time = None
        
        while True:
            ret, frame = self.cap.read()
            if not ret:
                print("❌ Failed to grab frame")
                break
            
            # Get single detection
            class_name, confidence, box = self.get_single_detection(frame)
            
            # Track detection duration
            current_detection = class_name or "None"
            if current_detection != last_detection:
                last_detection = current_detection
                detection_start_time = time.time()
            
            # Draw detection
            self.draw_single_detection(frame, class_name, confidence, box)
            
            # Draw settings info
            settings_text = f"Confidence threshold: {self.min_confidence:.0%}"
            cv2.putText(frame, settings_text, (10, frame.shape[0] - 60), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.6, (200, 200, 200), 2)
            
            # Draw detection duration
            if detection_start_time and current_detection != "None":
                duration = time.time() - detection_start_time
                duration_text = f"Detected for: {duration:.1f}s"
                cv2.putText(frame, duration_text, (10, frame.shape[0] - 90), 
                           cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 255), 2)
            
            # Show frame
            cv2.imshow('Single Detection - Indonesian Sign Language', frame)
            
            # Print detection info every 30 frames
            frame_count += 1
            if frame_count % 30 == 0:
                if class_name:
                    print(f"🎯 Detecting: {class_name} ({confidence:.1%})")
                else:
                    print("🔍 Searching for signs...")
            
            # Handle key presses
            key = cv2.waitKey(1) & 0xFF
            if key == ord('q'):
                break
            elif key == ord('s'):
                filename = f"single_detection_{int(time.time())}.jpg"
                cv2.imwrite(filename, frame)
                print(f"💾 Frame saved as {filename}")
            elif key == ord('+') or key == ord('='):
                self.min_confidence = min(0.95, self.min_confidence + 0.05)
                print(f"🎚️  Confidence threshold: {self.min_confidence:.0%}")
            elif key == ord('-'):
                self.min_confidence = max(0.1, self.min_confidence - 0.05)
                print(f"🎚️  Confidence threshold: {self.min_confidence:.0%}")
            elif key == ord('r'):
                self.min_confidence = 0.3
                print(f"🔄 Reset confidence threshold: {self.min_confidence:.0%}")
        
        self.cleanup()
    
    def cleanup(self):
        """Clean up resources"""
        if self.cap:
            self.cap.release()
        cv2.destroyAllWindows()
        print("🔚 Single detection camera closed")

def main():
    """Main function"""
    try:
        # You can adjust the minimum confidence here
        app = SingleDetectionCamera(
            model_path='sibiv3.pt', 
            camera_index=0,
            min_confidence=0.3  # 30% minimum confidence
        )
        app.run()
    except KeyboardInterrupt:
        print("\n⚠️  Application interrupted by user")
    except Exception as e:
        print(f"❌ Application error: {e}")

if __name__ == "__main__":
    main()
