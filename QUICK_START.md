# 🚀 Quick Start - Indonesian Sign Language Detection

## ⚡ Fastest Way to Run

1. **Install dependencies:**
   ```bash
   pip install ultralytics opencv-python
   ```

2. **Run the app:**
   ```bash
   python sign_language_camera.py
   ```

3. **Done!** 🎉

## 🎯 What You'll See

- **Real-time camera feed** with sign language detection
- **Bounding boxes** around detected signs
- **Class names** and **confidence scores**
- **9 Indonesian sign words**: `di`, `hotel`, `jalan`, `kamu`, `ke`, `makan`, `mana`, `mau`, `saya`

## 🎮 Controls

| Key | Action |
|-----|--------|
| `q` | Quit application |
| `s` | Save current frame |
| `c` | Print current detection to console |
| `h` | Show/hide help overlay |

## 🎨 Visual Indicators

- **🟢 Green box**: High confidence (>70%)
- **🟡 Yellow box**: Medium confidence (50-70%)
- **🟠 Orange box**: Low confidence (<50%)

## 📊 Example Output

```
🔄 Loading model from sibiv3.pt...
✅ Model loaded successfully!
📋 Classes: {0: 'di', 1: 'hotel', 2: 'jalan', 3: 'kamu', 4: 'ke', 5: 'makan', 6: 'mana', 7: 'mau', 8: 'saya'}
📹 Camera initialized successfully
🎥 Starting Indonesian Sign Language Detection...
📋 Detectable signs: ['di', 'hotel', 'jalan', 'kamu', 'ke', 'makan', 'mana', 'mau', 'saya']

📊 FPS: 28.5 | Detection: mana (0.89)
📊 FPS: 29.1 | Detection: saya (0.76)
```

## 🔧 Troubleshooting

### ❌ "Model not loaded"
```bash
pip install ultralytics
```

### ❌ "Camera not found"
- Try different camera index: edit `camera_index=1` in the code
- Make sure no other app is using the camera

### ❌ "Low FPS"
- Close other applications
- Use lower resolution camera settings

## 🎯 Tips for Better Detection

1. **Good lighting** - Make sure your hands are well-lit
2. **Clear background** - Avoid cluttered backgrounds
3. **Proper distance** - Keep hands at arm's length from camera
4. **Steady movements** - Hold signs clearly for 1-2 seconds

## 📁 Files Overview

- `sign_language_camera.py` ← **Main app (use this!)**
- `sibiv3.pt` ← Your YOLO model
- `requirements.txt` ← All dependencies
- Other files are for testing/debugging

---

**Ready to detect Indonesian sign language? Run the command above! 🚀**
