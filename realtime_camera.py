import cv2
import torch
import torch.nn.functional as F
import numpy as np
from torchvision import transforms
import time

class RealtimeCamera:
    def __init__(self, model_path='sibiv3.pt', camera_index=0):
        """
        Initialize realtime camera for model testing
        
        Args:
            model_path (str): Path to the PyTorch model file
            camera_index (int): Camera index (0 for default camera)
        """
        self.model_path = model_path
        self.camera_index = camera_index
        self.model = None
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        self.cap = None
        
        # Default image preprocessing transforms
        self.transform = transforms.Compose([
            transforms.ToPILImage(),
            transforms.Resize((224, 224)),  # Adjust size based on your model
            transforms.ToTensor(),
            transforms.Normalize(mean=[0.485, 0.456, 0.406], 
                               std=[0.229, 0.224, 0.225])
        ])
        
        # Class labels - adjust based on your model
        self.class_labels = [
            'Class_0', 'Class_1', 'Class_2', 'Class_3', 'Class_4'
        ]  # Replace with your actual class names
        
        self.load_model()
        self.setup_camera()
    
    def load_model(self):
        """Load the PyTorch model"""
        try:
            print(f"Loading model from {self.model_path}...")
            self.model = torch.load(self.model_path, map_location=self.device)
            self.model.eval()
            print(f"Model loaded successfully on {self.device}")
        except Exception as e:
            print(f"Error loading model: {e}")
            self.model = None
    
    def setup_camera(self):
        """Initialize camera"""
        try:
            self.cap = cv2.VideoCapture(self.camera_index)
            if not self.cap.isOpened():
                raise Exception("Cannot open camera")
            
            # Set camera properties
            self.cap.set(cv2.CAP_PROP_FRAME_WIDTH, 640)
            self.cap.set(cv2.CAP_PROP_FRAME_HEIGHT, 480)
            self.cap.set(cv2.CAP_PROP_FPS, 30)
            
            print("Camera initialized successfully")
        except Exception as e:
            print(f"Error initializing camera: {e}")
            self.cap = None
    
    def preprocess_frame(self, frame):
        """
        Preprocess frame for model inference
        
        Args:
            frame: OpenCV frame (BGR format)
            
        Returns:
            torch.Tensor: Preprocessed tensor ready for model
        """
        # Convert BGR to RGB
        frame_rgb = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
        
        # Apply transforms
        tensor = self.transform(frame_rgb)
        tensor = tensor.unsqueeze(0)  # Add batch dimension
        
        return tensor.to(self.device)
    
    def predict(self, frame):
        """
        Make prediction on frame
        
        Args:
            frame: OpenCV frame
            
        Returns:
            tuple: (predicted_class, confidence, all_probabilities)
        """
        if self.model is None:
            return "No Model", 0.0, []
        
        try:
            # Preprocess frame
            input_tensor = self.preprocess_frame(frame)
            
            # Make prediction
            with torch.no_grad():
                outputs = self.model(input_tensor)
                probabilities = F.softmax(outputs, dim=1)
                confidence, predicted = torch.max(probabilities, 1)
                
                predicted_class = predicted.item()
                confidence_score = confidence.item()
                
                # Get class name
                if predicted_class < len(self.class_labels):
                    class_name = self.class_labels[predicted_class]
                else:
                    class_name = f"Class_{predicted_class}"
                
                return class_name, confidence_score, probabilities[0].cpu().numpy()
                
        except Exception as e:
            print(f"Prediction error: {e}")
            return "Error", 0.0, []
    
    def draw_predictions(self, frame, class_name, confidence, probabilities):
        """
        Draw prediction results on frame
        
        Args:
            frame: OpenCV frame
            class_name: Predicted class name
            confidence: Confidence score
            probabilities: All class probabilities
        """
        height, width = frame.shape[:2]
        
        # Draw main prediction
        text = f"{class_name}: {confidence:.2f}"
        cv2.putText(frame, text, (10, 30), cv2.FONT_HERSHEY_SIMPLEX, 
                   1, (0, 255, 0), 2)
        
        # Draw all probabilities
        if len(probabilities) > 0:
            y_offset = 70
            for i, prob in enumerate(probabilities):
                if i < len(self.class_labels):
                    label = self.class_labels[i]
                else:
                    label = f"Class_{i}"
                
                prob_text = f"{label}: {prob:.3f}"
                color = (0, 255, 0) if prob == max(probabilities) else (255, 255, 255)
                cv2.putText(frame, prob_text, (10, y_offset), 
                           cv2.FONT_HERSHEY_SIMPLEX, 0.5, color, 1)
                y_offset += 25
        
        # Draw FPS
        fps_text = f"Device: {self.device}"
        cv2.putText(frame, fps_text, (width - 200, 30), 
                   cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 0), 1)
    
    def run(self):
        """Run the realtime camera application"""
        if self.cap is None:
            print("Camera not available")
            return
        
        if self.model is None:
            print("Model not loaded")
            return
        
        print("Starting realtime camera...")
        print("Press 'q' to quit, 's' to save current frame")
        
        frame_count = 0
        start_time = time.time()
        
        while True:
            ret, frame = self.cap.read()
            if not ret:
                print("Failed to grab frame")
                break
            
            # Make prediction
            class_name, confidence, probabilities = self.predict(frame)
            
            # Draw results
            self.draw_predictions(frame, class_name, confidence, probabilities)
            
            # Calculate and display FPS
            frame_count += 1
            if frame_count % 30 == 0:
                end_time = time.time()
                fps = 30 / (end_time - start_time)
                start_time = end_time
                print(f"FPS: {fps:.1f}, Prediction: {class_name} ({confidence:.2f})")
            
            # Display frame
            cv2.imshow('Realtime Model Testing', frame)
            
            # Handle key presses
            key = cv2.waitKey(1) & 0xFF
            if key == ord('q'):
                break
            elif key == ord('s'):
                # Save current frame
                filename = f"captured_frame_{int(time.time())}.jpg"
                cv2.imwrite(filename, frame)
                print(f"Frame saved as {filename}")
        
        self.cleanup()
    
    def cleanup(self):
        """Clean up resources"""
        if self.cap:
            self.cap.release()
        cv2.destroyAllWindows()
        print("Camera application closed")

def main():
    """Main function to run the application"""
    # You can modify these parameters
    model_path = 'sibiv3.pt'
    camera_index = 0  # 0 for default camera, 1 for external camera, etc.
    
    try:
        app = RealtimeCamera(model_path=model_path, camera_index=camera_index)
        app.run()
    except KeyboardInterrupt:
        print("\nApplication interrupted by user")
    except Exception as e:
        print(f"Application error: {e}")

if __name__ == "__main__":
    main()
