# Configuration file for realtime camera application

# Model Configuration
MODEL_PATH = 'sibiv3.pt'
INPUT_SIZE = (224, 224)  # Model input size (height, width)

# Camera Configuration
CAMERA_INDEX = 0  # 0 for default camera, 1 for external camera
CAMERA_WIDTH = 640
CAMERA_HEIGHT = 480
CAMERA_FPS = 60

# Class Labels - Indonesian Sign Language classes from your model
CLASS_LABELS = {
    0: 'di', 1: 'hotel', 2: 'jalan', 3: 'kamu', 4: 'ke',
    5: 'makan', 6: 'mana', 7: 'mau', 8: 'saya'
}

# Image Preprocessing
NORMALIZE_MEAN = [0.485, 0.456, 0.406]
NORMALIZE_STD = [0.229, 0.224, 0.225]

# Display Configuration
SHOW_ALL_PROBABILITIES = True
SHOW_FPS = True
CONFIDENCE_THRESHOLD = 0.5  # Minimum confidence to display prediction

# Detection Mode
SINGLE_DETECTION_MODE = True  # Only show highest confidence detection
MIN_CONFIDENCE_SINGLE = 0.25  # Minimum confidence for single detection mode
