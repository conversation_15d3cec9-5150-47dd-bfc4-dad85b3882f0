import cv2
import torch
import numpy as np

def load_yolo_model(model_path='sibiv3.pt'):
    """Load YOLO model with proper error handling"""
    try:
        print(f"Loading YOLO model from {model_path}...")
        
        # Try different loading methods for YOLO models
        try:
            # Method 1: Load with weights_only=False (for PyTorch 2.6+)
            model = torch.load(model_path, map_location='cpu', weights_only=False)
        except Exception as e1:
            print(f"Method 1 failed: {e1}")
            try:
                # Method 2: Load with pickle module
                import pickle
                model = torch.load(model_path, map_location='cpu', pickle_module=pickle)
            except Exception as e2:
                print(f"Method 2 failed: {e2}")
                return None
        
        print(f"✅ Model loaded successfully!")
        print(f"Model type: {type(model)}")
        
        # Check if it's a YOLO model
        if hasattr(model, 'predict'):
            print("✅ YOLO model detected (has predict method)")
        elif hasattr(model, 'model'):
            print("✅ Model wrapper detected")
        else:
            print("⚠️  Unknown model type")
        
        # Try to get class names
        if hasattr(model, 'names'):
            print(f"📋 Classes: {model.names}")
        
        return model
        
    except Exception as e:
        print(f"❌ Error loading model: {e}")
        return None

def predict_yolo(model, frame):
    """Make prediction using YOLO model"""
    try:
        if hasattr(model, 'predict'):
            # Use YOLO's built-in predict method
            results = model.predict(frame, verbose=False, conf=0.25)
            
            if len(results) > 0 and len(results[0].boxes) > 0:
                # Get the detection with highest confidence
                boxes = results[0].boxes
                confidences = boxes.conf.cpu().numpy()
                classes = boxes.cls.cpu().numpy().astype(int)
                
                # Find highest confidence detection
                max_conf_idx = confidences.argmax()
                predicted_class = classes[max_conf_idx]
                confidence_score = confidences[max_conf_idx]
                
                # Get class name
                if hasattr(model, 'names') and predicted_class in model.names:
                    class_name = model.names[predicted_class]
                else:
                    class_name = f"Class_{predicted_class}"
                
                # Get bounding box for visualization
                box = boxes.xyxy[max_conf_idx].cpu().numpy()
                
                return class_name, confidence_score, box, len(results[0].boxes)
            else:
                return "No Detection", 0.0, None, 0
        else:
            return "Model Error", 0.0, None, 0
            
    except Exception as e:
        print(f"Prediction error: {e}")
        return "Error", 0.0, None, 0

def draw_predictions(frame, class_name, confidence, box=None, num_detections=0):
    """Draw predictions on frame"""
    height, width = frame.shape[:2]
    
    # Draw main prediction text
    if confidence > 0:
        text = f"{class_name}: {confidence:.2f}"
        color = (0, 255, 0) if confidence > 0.5 else (0, 255, 255)
    else:
        text = class_name
        color = (0, 0, 255)
    
    cv2.putText(frame, text, (10, 30), cv2.FONT_HERSHEY_SIMPLEX, 
               1, color, 2)
    
    # Draw number of detections
    det_text = f"Detections: {num_detections}"
    cv2.putText(frame, det_text, (10, 70), cv2.FONT_HERSHEY_SIMPLEX, 
               0.7, (255, 255, 255), 2)
    
    # Draw bounding box if available
    if box is not None:
        x1, y1, x2, y2 = box.astype(int)
        cv2.rectangle(frame, (x1, y1), (x2, y2), color, 2)
        
        # Draw class name near the box
        cv2.putText(frame, f"{class_name} {confidence:.2f}", 
                   (x1, y1-10), cv2.FONT_HERSHEY_SIMPLEX, 
                   0.5, color, 1)

def main():
    """Main function"""
    # Load model
    model = load_yolo_model('sibiv3.pt')
    if model is None:
        print("Cannot load model. Exiting.")
        return
    
    # Initialize camera
    cap = cv2.VideoCapture(0)
    if not cap.isOpened():
        print("Cannot open camera. Exiting.")
        return
    
    # Set camera properties
    cap.set(cv2.CAP_PROP_FRAME_WIDTH, 640)
    cap.set(cv2.CAP_PROP_FRAME_HEIGHT, 480)
    
    print("🎥 Camera started!")
    print("Controls:")
    print("  'q' - Quit")
    print("  's' - Save current frame")
    print("  'c' - Print current prediction to console")
    
    frame_count = 0
    
    while True:
        ret, frame = cap.read()
        if not ret:
            print("Failed to grab frame")
            break
        
        # Make prediction
        class_name, confidence, box, num_detections = predict_yolo(model, frame)
        
        # Draw results
        draw_predictions(frame, class_name, confidence, box, num_detections)
        
        # Show frame
        cv2.imshow('YOLO Model Test', frame)
        
        # Print info every 30 frames
        frame_count += 1
        if frame_count % 30 == 0:
            print(f"Frame {frame_count}: {class_name} ({confidence:.3f}) - {num_detections} detections")
        
        # Handle key presses
        key = cv2.waitKey(1) & 0xFF
        if key == ord('q'):
            break
        elif key == ord('s'):
            filename = f"captured_frame_{frame_count}.jpg"
            cv2.imwrite(filename, frame)
            print(f"💾 Frame saved as {filename}")
        elif key == ord('c'):
            print(f"📊 Current: {class_name} (confidence: {confidence:.3f}, detections: {num_detections})")
    
    # Cleanup
    cap.release()
    cv2.destroyAllWindows()
    print("🔚 Application closed.")

if __name__ == "__main__":
    main()
