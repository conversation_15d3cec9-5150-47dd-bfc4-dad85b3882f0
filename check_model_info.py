import torch
import pickle

def analyze_model_file(model_path='sibiv3.pt'):
    """Analyze the model file to understand its structure"""
    
    print("=== MODEL ANALYSIS ===")
    
    try:
        # Load the model file
        print(f"Loading {model_path}...")
        model_data = torch.load(model_path, map_location='cpu', weights_only=False)
        
        print(f"✅ Model loaded successfully!")
        print(f"📋 Type: {type(model_data)}")
        
        if isinstance(model_data, dict):
            print(f"📁 Dictionary with {len(model_data)} keys:")
            for key in model_data.keys():
                print(f"   - {key}: {type(model_data[key])}")
                
                # Check if it's a tensor and show shape
                if isinstance(model_data[key], torch.Tensor):
                    print(f"     Shape: {model_data[key].shape}")
                elif isinstance(model_data[key], dict):
                    print(f"     Sub-dict with {len(model_data[key])} keys")
                    if len(model_data[key]) < 10:  # Show sub-keys if not too many
                        for sub_key in list(model_data[key].keys())[:5]:
                            print(f"       - {sub_key}")
                        if len(model_data[key]) > 5:
                            print(f"       ... and {len(model_data[key])-5} more")
                elif hasattr(model_data[key], '__len__'):
                    try:
                        print(f"     Length: {len(model_data[key])}")
                    except:
                        pass
            
            # Check for common model components
            print(f"\n🔍 Analysis:")
            
            if 'model' in model_data:
                print("   - Contains 'model' key - likely a complete model")
                model_obj = model_data['model']
                print(f"     Model type: {type(model_obj)}")
                
                if hasattr(model_obj, 'state_dict'):
                    print("     - Has state_dict method")
                if hasattr(model_obj, 'eval'):
                    print("     - Has eval method")
                if hasattr(model_obj, 'predict'):
                    print("     - Has predict method (YOLO-like)")
                if hasattr(model_obj, 'names'):
                    print(f"     - Has class names: {model_obj.names}")
                    
            elif any('weight' in key.lower() or 'bias' in key.lower() for key in model_data.keys()):
                print("   - Contains weight/bias keys - likely a state_dict")
                print("   - You need the model architecture to load this")
                
            if 'epoch' in model_data:
                print(f"   - Training epoch: {model_data['epoch']}")
            if 'optimizer' in model_data:
                print("   - Contains optimizer state")
            if 'best_fitness' in model_data:
                print(f"   - Best fitness: {model_data['best_fitness']}")
                
        else:
            print(f"📋 Model is not a dictionary")
            if hasattr(model_data, '__dict__'):
                attrs = [attr for attr in dir(model_data) if not attr.startswith('_')]
                print(f"   Attributes: {attrs[:10]}...")
                
        return model_data
        
    except Exception as e:
        print(f"❌ Error analyzing model: {e}")
        return None

def try_load_as_yolo(model_data):
    """Try to load as YOLO model"""
    print(f"\n=== TRYING YOLO LOADING ===")
    
    try:
        # Check if ultralytics is available
        try:
            from ultralytics import YOLO
            print("✅ Ultralytics available")
            
            # Try to load with ultralytics
            model = YOLO('sibiv3.pt')
            print("✅ Loaded with ultralytics YOLO!")
            
            if hasattr(model, 'names'):
                print(f"📋 Classes: {model.names}")
            
            return model
            
        except ImportError:
            print("❌ Ultralytics not installed")
            print("💡 Try: pip install ultralytics")
            
        except Exception as e:
            print(f"❌ Ultralytics loading failed: {e}")
            
        # Try manual loading if it's a model dict
        if isinstance(model_data, dict) and 'model' in model_data:
            print("🔄 Trying to extract model from dict...")
            model_obj = model_data['model']
            
            if hasattr(model_obj, 'eval'):
                model_obj.eval()
                print("✅ Model extracted and set to eval mode")
                return model_obj
                
    except Exception as e:
        print(f"❌ YOLO loading failed: {e}")
        
    return None

def main():
    # Analyze the model
    model_data = analyze_model_file()
    
    if model_data is not None:
        # Try to load as YOLO
        model = try_load_as_yolo(model_data)
        
        if model is not None:
            print(f"\n✅ SUCCESS! Model ready for use")
            print(f"Model type: {type(model)}")
        else:
            print(f"\n❌ Could not create usable model")
            print(f"💡 Recommendations:")
            print(f"   1. Install ultralytics: pip install ultralytics")
            print(f"   2. Or provide the model architecture code")
            print(f"   3. Check if this is a custom model that needs specific loading")

if __name__ == "__main__":
    main()
