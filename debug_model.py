import torch
import os

def debug_model(model_path='sibiv3.pt'):
    """Debug model loading issues"""
    
    print("=== MODEL DEBUG INFO ===")
    
    # Check if file exists
    print(f"1. Checking if model file exists: {model_path}")
    if os.path.exists(model_path):
        print("   ✅ Model file found")
        file_size = os.path.getsize(model_path) / (1024*1024)  # MB
        print(f"   📁 File size: {file_size:.2f} MB")
    else:
        print("   ❌ Model file NOT found")
        return
    
    # Check PyTorch version
    print(f"\n2. PyTorch version: {torch.__version__}")
    
    # Check device availability
    print(f"\n3. Device info:")
    print(f"   CUDA available: {torch.cuda.is_available()}")
    if torch.cuda.is_available():
        print(f"   CUDA device: {torch.cuda.get_device_name()}")
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"   Using device: {device}")
    
    # Try to load model with different methods
    print(f"\n4. Attempting to load model...")
    
    try:
        # Method 1: Direct load
        print("   Method 1: torch.load() with map_location")
        model = torch.load(model_path, map_location=device)
        print("   ✅ Model loaded successfully!")
        
        # Check model type
        print(f"   Model type: {type(model)}")
        
        # If it's a state dict, try to get more info
        if isinstance(model, dict):
            print("   📋 Model is a dictionary (state_dict)")
            print(f"   Keys: {list(model.keys())[:5]}...")  # Show first 5 keys
        else:
            print("   🧠 Model is a complete model object")
            try:
                model.eval()
                print("   ✅ Model set to eval mode")
            except Exception as e:
                print(f"   ⚠️  Could not set eval mode: {e}")
        
        return model
        
    except Exception as e:
        print(f"   ❌ Method 1 failed: {e}")
        
        # Method 2: Load with CPU first
        try:
            print("   Method 2: torch.load() with CPU first")
            model = torch.load(model_path, map_location='cpu')
            print("   ✅ Model loaded on CPU!")
            
            if device.type == 'cuda':
                try:
                    model = model.to(device)
                    print("   ✅ Model moved to GPU!")
                except Exception as e:
                    print(f"   ⚠️  Could not move to GPU: {e}")
            
            return model
            
        except Exception as e:
            print(f"   ❌ Method 2 failed: {e}")
            
            # Method 3: Check if it's a pickle issue
            try:
                print("   Method 3: Loading with pickle_module")
                import pickle
                model = torch.load(model_path, map_location=device, pickle_module=pickle)
                print("   ✅ Model loaded with pickle!")
                return model
            except Exception as e:
                print(f"   ❌ Method 3 failed: {e}")
    
    print("\n❌ All loading methods failed!")
    return None

def test_model_inference(model, device):
    """Test if model can do inference"""
    if model is None:
        return
    
    print(f"\n5. Testing model inference...")
    
    try:
        # Create dummy input (adjust size as needed)
        dummy_input = torch.randn(1, 3, 224, 224).to(device)
        print(f"   Created dummy input: {dummy_input.shape}")
        
        with torch.no_grad():
            if isinstance(model, dict):
                print("   ⚠️  Model is state_dict, cannot test inference directly")
                print("   💡 You may need to load this into a model architecture")
            else:
                model.eval()
                output = model(dummy_input)
                print(f"   ✅ Inference successful! Output shape: {output.shape}")
                
                # Check output
                if len(output.shape) == 2:  # (batch, classes)
                    num_classes = output.shape[1]
                    print(f"   📊 Number of classes: {num_classes}")
                    
                    # Show probabilities
                    probs = torch.softmax(output, dim=1)
                    top_prob, top_class = torch.max(probs, 1)
                    print(f"   🎯 Top prediction: Class {top_class.item()} (prob: {top_prob.item():.3f})")
                
    except Exception as e:
        print(f"   ❌ Inference test failed: {e}")

if __name__ == "__main__":
    model = debug_model()
    if model is not None:
        device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        test_model_inference(model, device)
    
    print(f"\n=== DEBUG COMPLETE ===")
