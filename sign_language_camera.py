import cv2
import numpy as np
from ultralytics import YOLO
import time

class SignLanguageCamera:
    def __init__(self, model_path='sibiv3.pt', camera_index=0):
        """
        Indonesian Sign Language Detection Camera
        
        Args:
            model_path (str): Path to YOLO model
            camera_index (int): Camera index
        """
        self.model_path = model_path
        self.camera_index = camera_index
        self.model = None
        self.cap = None
        
        # Indonesian Sign Language classes
        self.class_names = {
            0: 'di', 1: 'hotel', 2: 'jalan', 3: 'kamu', 4: 'ke', 
            5: 'makan', 6: 'mana', 7: 'mau', 8: 'saya'
        }
        
        self.load_model()
        self.setup_camera()
    
    def load_model(self):
        """Load YOLO model"""
        try:
            print(f"🔄 Loading model from {self.model_path}...")
            self.model = YOLO(self.model_path)
            print(f"✅ Model loaded successfully!")
            print(f"📋 Classes: {self.model.names}")
            
        except Exception as e:
            print(f"❌ Error loading model: {e}")
            print("💡 Make sure ultralytics is installed: pip install ultralytics")
            self.model = None
    
    def setup_camera(self):
        """Initialize camera"""
        try:
            self.cap = cv2.VideoCapture(self.camera_index)
            if not self.cap.isOpened():
                raise Exception("Cannot open camera")
            
            # Set camera properties
            self.cap.set(cv2.CAP_PROP_FRAME_WIDTH, 640)
            self.cap.set(cv2.CAP_PROP_FRAME_HEIGHT, 480)
            self.cap.set(cv2.CAP_PROP_FPS, 30)
            
            print("📹 Camera initialized successfully")
            
        except Exception as e:
            print(f"❌ Error initializing camera: {e}")
            self.cap = None
    
    def predict(self, frame):
        """
        Make prediction on frame
        
        Args:
            frame: OpenCV frame
            
        Returns:
            results: YOLO results object
        """
        if self.model is None:
            return None
        
        try:
            # Run inference
            results = self.model.predict(
                frame, 
                conf=0.25,      # Confidence threshold
                iou=0.45,       # IoU threshold for NMS
                verbose=False   # Don't print results
            )
            return results[0] if len(results) > 0 else None
            
        except Exception as e:
            print(f"❌ Prediction error: {e}")
            return None
    
    def draw_results(self, frame, results):
        """
        Draw detection results on frame
        
        Args:
            frame: OpenCV frame
            results: YOLO results
        """
        if results is None or len(results.boxes) == 0:
            # No detections
            cv2.putText(frame, "No sign detected", (10, 30), 
                       cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 0, 255), 2)
            return frame, "No Detection", 0.0
        
        # Get detection data
        boxes = results.boxes.xyxy.cpu().numpy()  # Bounding boxes
        confidences = results.boxes.conf.cpu().numpy()  # Confidence scores
        classes = results.boxes.cls.cpu().numpy().astype(int)  # Class indices
        
        best_detection = None
        best_confidence = 0
        
        # Draw all detections
        for i, (box, conf, cls) in enumerate(zip(boxes, confidences, classes)):
            x1, y1, x2, y2 = box.astype(int)
            
            # Get class name
            class_name = self.model.names.get(cls, f"Class_{cls}")
            
            # Track best detection
            if conf > best_confidence:
                best_confidence = conf
                best_detection = class_name
            
            # Choose color based on confidence
            if conf > 0.7:
                color = (0, 255, 0)  # Green - high confidence
            elif conf > 0.5:
                color = (0, 255, 255)  # Yellow - medium confidence
            else:
                color = (0, 165, 255)  # Orange - low confidence
            
            # Draw bounding box
            cv2.rectangle(frame, (x1, y1), (x2, y2), color, 2)
            
            # Draw label with confidence
            label = f"{class_name}: {conf:.2f}"
            label_size = cv2.getTextSize(label, cv2.FONT_HERSHEY_SIMPLEX, 0.6, 2)[0]
            
            # Draw label background
            cv2.rectangle(frame, (x1, y1 - label_size[1] - 10), 
                         (x1 + label_size[0], y1), color, -1)
            
            # Draw label text
            cv2.putText(frame, label, (x1, y1 - 5), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 0, 0), 2)
        
        # Draw summary info
        summary = f"Detections: {len(boxes)} | Best: {best_detection} ({best_confidence:.2f})"
        cv2.putText(frame, summary, (10, frame.shape[0] - 20), 
                   cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 255), 2)
        
        return frame, best_detection or "Multiple", best_confidence
    
    def run(self):
        """Run the sign language detection camera"""
        if self.cap is None or self.model is None:
            print("❌ Camera or model not available")
            return
        
        print("🎥 Starting Indonesian Sign Language Detection...")
        print("📋 Detectable signs:", list(self.model.names.values()))
        print("\nControls:")
        print("  'q' - Quit")
        print("  's' - Save current frame")
        print("  'c' - Print current detection")
        print("  'h' - Show/hide help")
        
        frame_count = 0
        start_time = time.time()
        show_help = True
        
        while True:
            ret, frame = self.cap.read()
            if not ret:
                print("❌ Failed to grab frame")
                break
            
            # Make prediction
            results = self.predict(frame)
            
            # Draw results
            frame, best_class, best_conf = self.draw_results(frame, results)
            
            # Calculate FPS
            frame_count += 1
            if frame_count % 30 == 0:
                end_time = time.time()
                fps = 30 / (end_time - start_time)
                start_time = end_time
                print(f"📊 FPS: {fps:.1f} | Detection: {best_class} ({best_conf:.2f})")
            
            # Draw FPS
            fps_text = f"FPS: {frame_count / (time.time() - start_time + 0.001):.1f}"
            cv2.putText(frame, fps_text, (frame.shape[1] - 120, 30), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 0), 2)
            
            # Show help overlay
            if show_help:
                help_text = [
                    "Indonesian Sign Language Detection",
                    "Press 'h' to hide this help",
                    "Press 'q' to quit"
                ]
                for i, text in enumerate(help_text):
                    cv2.putText(frame, text, (10, 60 + i*25), 
                               cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 255), 1)
            
            # Display frame
            cv2.imshow('Indonesian Sign Language Detection', frame)
            
            # Handle key presses
            key = cv2.waitKey(1) & 0xFF
            if key == ord('q'):
                break
            elif key == ord('s'):
                filename = f"sign_detection_{int(time.time())}.jpg"
                cv2.imwrite(filename, frame)
                print(f"💾 Frame saved as {filename}")
            elif key == ord('c'):
                print(f"📊 Current detection: {best_class} (confidence: {best_conf:.3f})")
            elif key == ord('h'):
                show_help = not show_help
        
        self.cleanup()
    
    def cleanup(self):
        """Clean up resources"""
        if self.cap:
            self.cap.release()
        cv2.destroyAllWindows()
        print("🔚 Sign language detection closed")

def main():
    """Main function"""
    try:
        app = SignLanguageCamera(model_path='sibiv3.pt', camera_index=0)
        app.run()
    except KeyboardInterrupt:
        print("\n⚠️  Application interrupted by user")
    except Exception as e:
        print(f"❌ Application error: {e}")

if __name__ == "__main__":
    main()
