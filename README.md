# Indonesian Sign Language Detection Camera

Aplikasi realtime camera untuk testing model YOLO deteksi bahasa isyarat Indonesia.

## Model Information

- **Model**: YOLO v8 untuk deteksi bahasa isyarat Indonesia
- **Classes**: 9 kata bahasa isyarat: `di`, `hotel`, `jalan`, `kamu`, `ke`, `makan`, `mana`, `mau`, `saya`
- **Format**: YOLOv8 PyTorch model (`.pt`)

## File yang Dibuat

1. **`sign_language_camera.py`** - ✅ **RECOMMENDED** - Aplikasi utama untuk deteksi bahasa isyarat
2. **`realtime_camera.py`** - Aplikasi generic (untuk model klasifikasi)
3. **`yolo_camera_test.py`** - Test aplikasi YOLO
4. **`simple_camera_test.py`** - Versi sederhana generic
5. **`config.py`** - File konfigurasi
6. **`debug_model.py`** - Tool untuk debug model
7. **`check_model_info.py`** - Tool untuk analisis model
8. **`requirements.txt`** - Dependencies yang diperlukan

## Instalasi

1. Install dependencies:

```bash
pip install -r requirements.txt
```

## Konfigurasi

Edit file `config.py` untuk menyesuaikan:

- **MODEL_PATH**: Path ke file model Anda
- **CLASS_LABELS**: Daftar nama kelas sesuai model Anda
- **INPUT_SIZE**: Ukuran input model (height, width)
- **CAMERA_INDEX**: Index kamera (0 untuk default, 1 untuk eksternal)

## Cara Menggunakan

### ✅ Aplikasi Utama (RECOMMENDED)

```bash
python sign_language_camera.py
```

### Alternatif lain:

```bash
# Untuk model klasifikasi generic
python realtime_camera.py

# Versi test YOLO
python yolo_camera_test.py

# Versi sederhana generic
python simple_camera_test.py
```

## Kontrol

- **'q'**: Keluar dari aplikasi
- **'s'**: Simpan frame saat ini (hanya di versi lengkap)

## Fitur

### Versi Sederhana (`simple_camera_test.py`)

- ✅ Load model PyTorch
- ✅ Realtime camera feed
- ✅ Prediksi realtime
- ✅ Tampilan confidence score
- ✅ Threshold confidence

### Versi Lengkap (`realtime_camera.py`)

- ✅ Semua fitur versi sederhana
- ✅ Tampilan semua probabilitas kelas
- ✅ FPS counter
- ✅ Save frame
- ✅ Error handling yang lebih baik
- ✅ Preprocessing yang lebih advanced

## Troubleshooting

### Model tidak bisa di-load

- Pastikan file `sibiv3.pt` ada di direktori yang sama
- Pastikan model compatible dengan versi PyTorch yang terinstall

### Camera tidak bisa dibuka

- Coba ubah `CAMERA_INDEX` di `config.py` (0, 1, 2, dst.)
- Pastikan camera tidak digunakan aplikasi lain
- Pastikan camera driver terinstall

### Prediksi tidak akurat

- Sesuaikan `INPUT_SIZE` dengan ukuran input model
- Update `CLASS_LABELS` dengan nama kelas yang benar
- Sesuaikan preprocessing jika diperlukan

### Performance lambat

- Gunakan GPU jika tersedia (CUDA)
- Kurangi resolusi camera di `config.py`
- Gunakan versi sederhana untuk testing cepat

## Customization

Untuk menyesuaikan dengan model Anda:

1. **Update Class Labels**: Edit `CLASS_LABELS` di `config.py`
2. **Sesuaikan Input Size**: Edit `INPUT_SIZE` sesuai model
3. **Preprocessing**: Modifikasi fungsi `preprocess_frame()` jika diperlukan
4. **Threshold**: Sesuaikan `CONFIDENCE_THRESHOLD` untuk filtering prediksi

## Contoh Output

```
Model loaded on cuda
Camera started. Press 'q' to quit.
FPS: 28.5, Prediction: A (0.89)
FPS: 29.1, Prediction: B (0.76)
```

## Requirements

- Python 3.7+
- PyTorch
- OpenCV
- NumPy
- Camera/Webcam
