# Realtime Camera untuk Testing Model

Aplikasi sederhana untuk testing model PyTorch menggunakan realtime camera.

## File yang Dibuat

1. **`realtime_camera.py`** - Aplikasi lengkap dengan fitur advanced
2. **`simple_camera_test.py`** - Versi sederhana dan mudah digunakan
3. **`config.py`** - File konfigurasi
4. **`requirements.txt`** - Dependencies yang diperlukan

## Instalasi

1. Install dependencies:
```bash
pip install -r requirements.txt
```

## Konfigurasi

Edit file `config.py` untuk menyesuaikan:

- **MODEL_PATH**: Path ke file model Anda
- **CLASS_LABELS**: Daftar nama kelas sesuai model Anda
- **INPUT_SIZE**: Ukuran input model (height, width)
- **CAMERA_INDEX**: Index kamera (0 untuk default, 1 untuk eksternal)

## Cara Menggunakan

### Versi <PERSON>hana (Recommended untuk testing cepat)
```bash
python simple_camera_test.py
```

### Versi Lengkap (Dengan fitur advanced)
```bash
python realtime_camera.py
```

## Kontrol

- **'q'**: Keluar dari aplikasi
- **'s'**: Simpan frame saat ini (hanya di versi lengkap)

## Fitur

### Versi Sederhana (`simple_camera_test.py`)
- ✅ Load model PyTorch
- ✅ Realtime camera feed
- ✅ Prediksi realtime
- ✅ Tampilan confidence score
- ✅ Threshold confidence

### Versi Lengkap (`realtime_camera.py`)
- ✅ Semua fitur versi sederhana
- ✅ Tampilan semua probabilitas kelas
- ✅ FPS counter
- ✅ Save frame
- ✅ Error handling yang lebih baik
- ✅ Preprocessing yang lebih advanced

## Troubleshooting

### Model tidak bisa di-load
- Pastikan file `sibiv3.pt` ada di direktori yang sama
- Pastikan model compatible dengan versi PyTorch yang terinstall

### Camera tidak bisa dibuka
- Coba ubah `CAMERA_INDEX` di `config.py` (0, 1, 2, dst.)
- Pastikan camera tidak digunakan aplikasi lain
- Pastikan camera driver terinstall

### Prediksi tidak akurat
- Sesuaikan `INPUT_SIZE` dengan ukuran input model
- Update `CLASS_LABELS` dengan nama kelas yang benar
- Sesuaikan preprocessing jika diperlukan

### Performance lambat
- Gunakan GPU jika tersedia (CUDA)
- Kurangi resolusi camera di `config.py`
- Gunakan versi sederhana untuk testing cepat

## Customization

Untuk menyesuaikan dengan model Anda:

1. **Update Class Labels**: Edit `CLASS_LABELS` di `config.py`
2. **Sesuaikan Input Size**: Edit `INPUT_SIZE` sesuai model
3. **Preprocessing**: Modifikasi fungsi `preprocess_frame()` jika diperlukan
4. **Threshold**: Sesuaikan `CONFIDENCE_THRESHOLD` untuk filtering prediksi

## Contoh Output

```
Model loaded on cuda
Camera started. Press 'q' to quit.
FPS: 28.5, Prediction: A (0.89)
FPS: 29.1, Prediction: B (0.76)
```

## Requirements

- Python 3.7+
- PyTorch
- OpenCV
- NumPy
- Camera/Webcam
