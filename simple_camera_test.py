import cv2
import torch
import numpy as np
from torchvision import transforms
import config

def load_model(model_path):
    """Load PyTorch model"""
    try:
        device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        model = torch.load(model_path, map_location=device)
        model.eval()
        print(f"Model loaded on {device}")
        return model, device
    except Exception as e:
        print(f"Error loading model: {e}")
        return None, None

def preprocess_frame(frame, input_size=(224, 224)):
    """Simple preprocessing for frame"""
    # Resize frame
    resized = cv2.resize(frame, input_size)
    
    # Convert BGR to RGB
    rgb = cv2.cvtColor(resized, cv2.COLOR_BGR2RGB)
    
    # Convert to tensor and normalize
    tensor = torch.from_numpy(rgb).float()
    tensor = tensor.permute(2, 0, 1)  # HWC to CHW
    tensor = tensor / 255.0  # Normalize to [0, 1]
    tensor = tensor.unsqueeze(0)  # Add batch dimension
    
    return tensor

def predict(model, frame, device, class_labels):
    """Make prediction on frame"""
    try:
        # Preprocess
        input_tensor = preprocess_frame(frame, config.INPUT_SIZE)
        input_tensor = input_tensor.to(device)
        
        # Predict
        with torch.no_grad():
            outputs = model(input_tensor)
            probabilities = torch.softmax(outputs, dim=1)
            confidence, predicted = torch.max(probabilities, 1)
            
            predicted_class = predicted.item()
            confidence_score = confidence.item()
            
            if predicted_class < len(class_labels):
                class_name = class_labels[predicted_class]
            else:
                class_name = f"Class_{predicted_class}"
            
            return class_name, confidence_score
            
    except Exception as e:
        print(f"Prediction error: {e}")
        return "Error", 0.0

def main():
    # Load model
    model, device = load_model(config.MODEL_PATH)
    if model is None:
        print("Cannot load model. Exiting.")
        return
    
    # Initialize camera
    cap = cv2.VideoCapture(config.CAMERA_INDEX)
    if not cap.isOpened():
        print("Cannot open camera. Exiting.")
        return
    
    cap.set(cv2.CAP_PROP_FRAME_WIDTH, config.CAMERA_WIDTH)
    cap.set(cv2.CAP_PROP_FRAME_HEIGHT, config.CAMERA_HEIGHT)
    
    print("Camera started. Press 'q' to quit.")
    
    while True:
        ret, frame = cap.read()
        if not ret:
            break
        
        # Make prediction
        class_name, confidence = predict(model, frame, device, config.CLASS_LABELS)
        
        # Draw prediction on frame
        if confidence > config.CONFIDENCE_THRESHOLD:
            text = f"{class_name}: {confidence:.2f}"
            color = (0, 255, 0)  # Green
        else:
            text = f"Low confidence: {confidence:.2f}"
            color = (0, 0, 255)  # Red
        
        cv2.putText(frame, text, (10, 30), cv2.FONT_HERSHEY_SIMPLEX, 
                   1, color, 2)
        
        # Show frame
        cv2.imshow('Simple Model Test', frame)
        
        # Check for quit
        if cv2.waitKey(1) & 0xFF == ord('q'):
            break
    
    # Cleanup
    cap.release()
    cv2.destroyAllWindows()
    print("Application closed.")

if __name__ == "__main__":
    main()
